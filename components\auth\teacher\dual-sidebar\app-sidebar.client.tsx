'use client';
import { ComponentProps, useState, useCallback } from "react";
import { motion, AnimatePresence } from 'motion/react';
import { cn } from "../../../../lib/utils";
import { useIsMobile } from "../../../../hooks/use-mobile";
import { useRedirect } from 'blade/hooks';

import {
  Sidebar,
  SidebarHeader,
  SidebarContent,
  SidebarFooter,
} from "../../../ui/sidebar.client";
import { Sheet, SheetContent, SheetTitle } from "../../../ui/sheet.client";
import { NavMain } from "./nav-main.client";
import { NavProjects } from "./nav-projects.client";
import { NavUser } from "./nav-user.client";
import { TeamSwitcher } from "./team-switcher.client";
// import { CanvasRevealEffect } from "../../../ui/canvas-reveal-effect.client"; // Temporarily disabled due to React compatibility
import { <PERSON>auge } from "@suyalcinkaya/gauge";
import {
  Bell,
  Plus,
  Compass,
  PlayCircle,
  GalleryVerticalEnd,
  SquareTerminal,
  Bot,
  BookOpen,
  Settings2,
  Frame,
  PieChart,
  Map,
  Brain,
  Circle,
} from "lucide-react";
import { Image } from 'blade/client/components';


interface AppSidebarProps extends ComponentProps<"div"> {
  flyout: string | null;
  setFlyout: (flyout: string | null) => void;
  variant?: "sidebar" | "floating" | "inset";
  collapsible?: "offcanvas" | "icon" | "none";
}

interface IconItem {
  id: string;
  icon: any;
  label: string;
  tooltip: string;
  section: number;
  url?: string; // Optional URL for navigation
}

const iconItems: IconItem[] = [
  // First section - Notifications
  {
    id: "calendar",
    icon: Bell,
    label: "Calendar",
    tooltip: "Calendar",
    section: 1,
    url: "/teacher/calendar" // Optional: navigate to notifications page
  },
  // Second section - Navigation
  {
    id: "classes",
    icon: PlayCircle,
    label: "Classes",
    tooltip: "Your Classes",
    section: 2,
    url: "/teacher/classes" // Navigate to runs page
  },
  {
    id: "discover",
    icon: Compass,
    label: "Discover",
    tooltip: "Discover",
    section: 2,
    url: "/teacher/discover" // Navigate to discover page
  },
  // Third section - Create New
  {
    id: "new",
    icon: Plus,
    label: "New",
    tooltip: "Create New",
    section: 3,
    url: "/teacher/create" // Navigate to create page
  },
  // Fourth section - Status (above canvas reveal)
  {
    id: "status",
    icon: Circle,
    label: "Status",
    tooltip: "System Status",
    section: 4,
  },
];

function IconButton({
  item,
  isActive,
  onClick,
  isMobile = false
}: {
  item: IconItem;
  isActive: boolean;
  onClick: () => void;
  isMobile?: boolean;
}) {
  const [isHovered, setIsHovered] = useState(false);
  const redirect = useRedirect();

  const handleButtonClick = (e: React.MouseEvent) => {
    // Always toggle the flyout
    onClick();

    // If there's a URL, navigate to it
    if (item.url) {
      redirect(item.url);
    }
  };

  // Alternative approach: You could also add different behaviors per item
  // For example, some items might only navigate, others might only toggle flyout
  // const handleClick = () => {
  //   switch (item.id) {
  //     case 'notifications':
  //       // Only toggle flyout for notifications
  //       onClick();
  //       break;
  //     case 'projects':
  //       // Both toggle flyout and navigate for projects
  //       onClick();
  //       if (item.url) redirect(item.url);
  //       break;
  //     case 'runs':
  //       // Only navigate for runs (no flyout)
  //       if (item.url) redirect(item.url);
  //       break;
  //     default:
  //       // Default behavior: toggle flyout and navigate if URL exists
  //       onClick();
  //       if (item.url) redirect(item.url);
  //   }
  // };

  const ButtonContent = () => (
    <>
      {/* Main glassmorphism background */}
      <div
        className="absolute inset-0 rounded-xl transition-all duration-300 ease-out backdrop-blur-[32px] backdrop-saturate-[180%]"
        style={{
          background: isActive
            ? `
              linear-gradient(135deg,
                rgba(0, 0, 0, 0.12) 0%,
                rgba(0, 0, 0, 0.08) 30%,
                rgba(0, 0, 0, 0.04) 70%,
                rgba(0, 0, 0, 0.02) 100%
              ),
            radial-gradient(circle at 30% 30%,
                          rgba(255, 255, 255, 0.15) 0%,
                          transparent 70%
                        )
            `
            : isHovered
            ? `
              linear-gradient(135deg,
                          rgba(255, 255, 255, 0.06) 0%,
                          rgba(255, 255, 255, 0.03) 50%,
                          rgba(255, 255, 255, 0.01) 100%
                        )
            `
            : 'transparent',
          border: isActive
            ? '1px solid rgba(255, 255, 255, 0.1)'
            : isHovered
            ? '1px solid rgba(255, 255, 255, 0.05)'
            : '1px solid transparent',
        } as React.CSSProperties}
      />

      {/* Icon with enhanced styling */}
      <item.icon
        className={cn(
          "w-5 h-5 relative z-10 transition-all duration-300 ease-out",
          isActive
            ? "text-blue dark:text-white drop-shadow-[0_2px_8px_rgba(0,0,0,0.4)] dark:drop-shadow-[0_2px_8px_rgba(255,255,255,0.4)] scale-110"
            : isHovered
            ? "text-black/90 dark:text-white/90 scale-105"
            : "text-black/60 dark:text-white/60"
        )}
      />
    </>
  );

  return (
    <div className="relative group">
      <button
        onClick={handleButtonClick}
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
        className={cn(
          "relative w-12 h-12 flex items-center justify-center rounded-xl transition-all duration-300 ease-out transform-gpu will-change-transform overflow-hidden",
          "z-20 touch-manipulation select-none outline-none"
        )}
        style={{
          transform: isActive ? 'scale(1.05)' : isHovered ? 'scale(1.02)' : 'scale(1)',
          WebkitTapHighlightColor: 'transparent',
          WebkitTouchCallout: 'none',
          WebkitUserSelect: 'none',
          userSelect: 'none',
          touchAction: 'manipulation',
          cursor: 'pointer',
          pointerEvents: 'auto'
        } as React.CSSProperties}
        type="button"
        aria-pressed={isActive}
        aria-label={item.tooltip}
      >
        <ButtonContent />
      </button>

      {/* Enhanced active indicator */}
      <div
        className="absolute -right-1 top-[28px] transform -translate-y-1/2 transition-all duration-300 ease-out z-20"
        style={{
          opacity: isActive ? 1 : 0,
          transform: `translateY(-50%) scale(${isActive ? 1 : 0.8})`,
        } as React.CSSProperties}
      >
        <div
          className="w-1.5 h-1.5 rounded-full bg-black dark:bg-white"
          style={{
            boxShadow: '0 0 12px rgba(0, 0, 0, 0.6), 0 0 24px rgba(0, 0, 0, 0.3)',
            filter: 'blur(0.5px)',
          } as React.CSSProperties}
        />
      </div>

      {/* Hover tooltip */}
      <div
        className="absolute left-16 top-[2.5rem] font-manrope_1 transform -translate-y-1/2 bg-gradient-to-b from-[#f8f9fa] via-[#f8f9fa] to-[#e9ecef] dark:from-[#212026] dark:via-[#212026] dark:to-[#29282e] px-3 text-sm leading-8 text-black/80 dark:text-white/80 dark:shadow-[0_-1px_0_1px_rgba(255,255,255,0.8)_inset,0_0_0_1px_rgb(226_232_240)_inset,0_0.5px_0_1.5px_#64748b_inset] shadow-[0_-1px_0_1px_rgba(0,0,0,0.8)_inset,0_0_0_1px_rgb(9_9_11)_inset,0_0.5px_0_1.5px_#71717a_inset] rounded-lg pointer-events-none transition-all duration-300 ease-out whitespace-nowrap z-50"
        style={{
          opacity: isHovered && !isActive ? 1 : 0,
          transform: `translate(${isHovered && !isActive ? '0' : '-8px'}, -50%)`,
          backdropFilter: 'blur(10px)',
          boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',
        } as React.CSSProperties}
      >
        {item.tooltip}
        <div
          className="absolute left-0 top-1/2 -translate-x-1 -translate-y-1/2 w-0 h-0 dark:border-r-[#e9ecef] border-r-[#212026]"
          style={{
            borderTop: '6px solid transparent',
            borderBottom: '6px solid transparent',
            borderRightWidth: '6px',
            borderRightStyle: 'solid',
          } as React.CSSProperties}
        />
      </div>
    </div>
  );
}
// Add a Separator component
function Separator() {
  return (
    <div className="w-8 h-px bg-black/10 dark:bg-white/10 my-2 mx-auto" />
  );
}

export function AppSidebar({
  flyout,
  setFlyout,
  variant = "sidebar",
  collapsible = "none",
  className,
  ...props
}: AppSidebarProps) {
  const [activeItem, setActiveItem] = useState<string | null>("projects");
  const isMobile = useIsMobile();

  // Simplified toggle handler without debouncing - matches working right-sidebar pattern
  const handleToggleFlyout = useCallback((itemId: string) => {
    console.log(`🎯 Toggle flyout for ${itemId}:`, { currentFlyout: flyout, itemId });
    const newFlyout = flyout === itemId ? null : itemId;
    console.log(`🎯 Setting flyout to:`, newFlyout);
    setFlyout(newFlyout);
  }, [flyout, setFlyout]);

  const data = {
    user: {
      name: "School Admin",
      email: "<EMAIL>",
      avatar: "/avatars/admin.jpg",
    },
    teams: [
      {
        name: "School District",
        logo: GalleryVerticalEnd,
        plan: "Education",
      },
    ],
    navMain: [
      {
        title: "Dashboard",
        url: "#",
        icon: SquareTerminal,
        isActive: true,
        items: [
          {
            title: "Overview",
            url: "#",
          },
          {
            title: "Analytics",
            url: "#",
          },
          {
            title: "Reports",
            url: "#",
          },
        ],
      },
      {
        title: "Students",
        url: "#",
        icon: Bot,
        items: [
          {
            title: "All Students",
            url: "#",
          },
          {
            title: "Enrollment",
            url: "#",
          },
          {
            title: "Attendance",
            url: "#",
          },
        ],
      },
      {
        title: "Teachers",
        url: "#",
        icon: BookOpen,
        items: [
          {
            title: "All Teachers",
            url: "#",
          },
          {
            title: "Schedules",
            url: "#",
          },
          {
            title: "Performance",
            url: "#",
          },
        ],
      },
      {
        title: "Settings",
        url: "#",
        icon: Settings2,
        items: [
          {
            title: "General",
            url: "#",
          },
          {
            title: "Users",
            url: "#",
          },
          {
            title: "Security",
            url: "#",
          },
        ],
      },
    ],
    projects: [
      {
        name: "Academic Year 2024",
        url: "#",
        icon: Frame,
      },
      {
        name: "Summer Programs",
        url: "#",
        icon: PieChart,
      },
      {
        name: "Extracurriculars",
        url: "#",
        icon: Map,
      },
    ],
  };

  if (collapsible === "icon") {
    return (
      <div className="flex h-full">
        {/* Icon Rail */}
        <div className="flex flex-col w-18 h-svh z-30 fixed left-0 top-0  ">
          <div className="flex flex-col items-center gap-2 p-2">
            {/* Logo Section */}
            <div className="w-10 h-10 flex items-center justify-center  z-20">
              <Image
                    src="/logo-lightmode.png"
                    alt="Penned Logo"
                    className="w-6 h-6 dark:hidden"
                    width={28}
                    height={28}
                  />
                  {/* Dark mode logo - shows in dark mode */}
                  <Image
                    src="/logo-darkmode.png"
                    alt="Penned Logo"
                    className="w-6 h-6 hidden dark:block"
                    width={28}
                    height={28}
                  />
                            
            </div>

            {/* Bell Icon */}
            {iconItems[0] && (
              <IconButton
                item={iconItems[0]}
                isActive={flyout === iconItems[0].id}
                onClick={() => handleToggleFlyout(iconItems[0]?.id || '')}
                isMobile={isMobile}
              />
            )}
            
            <Separator />
            
            {/* Navigation Section */}
            {iconItems
              .filter(item => item.section === 2)
              .map((item) => (
                <IconButton
                  key={item.id}
                  item={item}
                  isActive={flyout === item.id}
                  onClick={() => handleToggleFlyout(item.id)}
                  isMobile={isMobile}
                />
              ))}
            
            <Separator />
            
            {/* Create New Section */}
            {iconItems
              .filter(item => item.section === 3)
              .map((item) => (
                <IconButton
                  key={item.id}
                  item={item}
                  isActive={flyout === item.id}
                  onClick={() => handleToggleFlyout(item.id)}
                  isMobile={isMobile}
                />
              ))}

            <Separator />

            {/* Status Section */}
            {iconItems
              .filter(item => item.section === 4)
              .map((item) => (
                <IconButton
                  key={item.id}
                  item={item}
                  isActive={flyout === item.id}
                  onClick={() => handleToggleFlyout(item.id)}
                  isMobile={isMobile}
                />
              ))}

            {/* Student Count Gauge */}
            <div className="w-12 z-20">
              <button
                onClick={() => {
                  setFlyout(flyout === 'students-gauge' ? null : 'students-gauge');
                  // Navigate to students page
                  window.location.href = '/teacher/students';
                }}
                className="relative flex w-full items-center justify-center h-36 select-none rounded-xl overflow-hidden shadow-[0_-1px_0_1px_rgba(0,0,0,0.8)_inset,0_0_0_1px_rgb(9_9_11)_inset,0_0.5px_0_1.5px_#71717a_inset] transition-all duration-200 cursor-pointer group"
              >
                {/* Canvas Reveal Effect Background - TEMPORARILY DISABLED */}
                {/*
                <div className="absolute inset-0">
                  <CanvasRevealEffect
                    animationSpeed={3}
                    containerClassName="bg-black"
                    colors={[
                      [244, 244, 245], // zinc-100 - cool white (default for now)
                      [250, 250, 250], // white
                      [228, 228, 231]  // zinc-200
                    ]}
                    dotSize={1.5}
                    opacities={[0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9, 1.0, 1.0]}
                    showGradient={false}
                  />
                </div>
                */}

                {/* Temporary CSS Fallback Background */}
                <div className="absolute inset-0 bg-black overflow-hidden">
                  <div
                    className="absolute inset-0 animate-pulse opacity-30"
                    style={{
                      backgroundImage: `radial-gradient(circle at 1px 1px, rgba(244, 244, 245, 0.8) 1px, transparent 0)`,
                      backgroundSize: '12px 12px'
                    } as React.CSSProperties}
                  />
                  <div
                    className="absolute inset-0 animate-pulse opacity-20"
                    style={{
                      backgroundImage: `radial-gradient(circle at 1px 1px, rgba(250, 250, 250, 0.6) 1px, transparent 0)`,
                      backgroundSize: '16px 16px',
                      animationDelay: '1s'
                    } as React.CSSProperties}
                  />
                  <div
                    className="absolute inset-0 animate-pulse opacity-10"
                    style={{
                      backgroundImage: `radial-gradient(circle at 1px 1px, rgba(228, 228, 231, 0.4) 1px, transparent 0)`,
                      backgroundSize: '20px 20px',
                      animationDelay: '2s'
                    } as React.CSSProperties}
                  />
                </div>

                {/* Gauge at the top */}
                <div className="absolute top-2 left-1/2 transform -translate-x-1/2 z-20">
                  <div className="relative">
                    <Gauge
                      value={getStudentCountPercentage()}
                      size="sm"
                      showValue={true}
                      showAnimation={true}
                      primary="#f4f4f5" // Default color for now
                      secondary="rgba(255,255,255,0.2)"
                    />
                    {/* Custom percentage display overlay */}
                    <div className="absolute inset-0 flex items-center justify-center pointer-events-none">
                      <span
                        className="text-xs font-bold text-white"
                        style={{
                          textShadow: '0 1px 2px rgba(0,0,0,0.8), 0 0 4px rgba(0,0,0,0.5)',
                          filter: 'drop-shadow(0 0 2px rgba(0,0,0,0.9))'
                        }}
                      >
                        {Math.round(getStudentCountPercentage())}%
                      </span>
                    </div>
                  </div>
                </div>

                {/* Bottom fade overlay to make dots less visible at bottom */}
                <div
                  className="absolute inset-0 pointer-events-none"
                  style={{
                    background: `linear-gradient(to bottom, transparent 0%, transparent 30%, rgba(10,5,2,0.4) 50%, rgba(8,4,1,0.8) 100%)`
                  }}
                />

                {/* Brain icon at bottom */}
                <div className="absolute bottom-2 left-1/2 transform -translate-x-1/2 z-20">
                  <Brain className="w-4 h-4 text-zinc-50 group-hover:text-white transition-colors duration-200" />
                </div>

                <span className="relative z-10 text-xs font-medium writing-vertical-rl transform rotate-180 text-zinc-50">Students</span>
              </button>
            </div>
          </div>
        </div>

        {/* Flyout Panel */}
        {isMobile ? (
          <Sheet open={!!flyout} onOpenChange={(open) => !open && setFlyout(null)} modal={false}>
            <SheetContent
              side="left"
              className={cn(
                "mobile-sheet-content",
                " p-0 bg-gradient-to-r from-[#f8f8f8] via-[#f4f4f4] to-[#f0f0f0] dark:from-[#212123] dark:via-[#18181a] dark:to-[#141416]",
                "fixed left-[72px] top-0 h-full w-[calc(100vw-72px)] max-w-none",
                "z-10"
              )}
            >
              <SheetTitle className="sr-only">
                {flyout === 'projects' ? 'Projects' :
                 flyout === 'runs' ? 'Runs' :
                 flyout === 'discover' ? 'Discover' :
                 flyout === 'new' ? 'Create New' :
                 flyout === 'notifications' ? 'Notifications' :
                 flyout === 'status' ? 'System Status' :
                 flyout === 'students-gauge' ? 'Student Overview' :
                 'Navigation Panel'}
              </SheetTitle>
              <div className="h-full overflow-hidden">
                {renderFlyoutContent(flyout, data)}
              </div>
            </SheetContent>
          </Sheet>
        ) : (
          <>
            {/* Flyout separator line */}
            <AnimatePresence>
              {flyout && (
                <motion.div
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  exit={{ opacity: 0 }}
                  transition={{ type: "spring", stiffness: 300, damping: 30, mass: 0.8 }}
                  className="fixed left-[71px]    top-0 h-svh w-[1px] z-10"
                />
              )}
            </AnimatePresence>

            {/* Flyout content panel */}
            <AnimatePresence mode="wait">
              {flyout && (
                <motion.div
                  initial={{ opacity: 0, x: -100 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: -100 }}
                  transition={{ type: "spring", stiffness: 300, damping: 30, mass: 0.8 }}
                  className="fixed left-[72px] border-l border-black/20 dark:border-white/20  rounded-tl-4xl top-0 h-svh w-[278px] bg-gradient-to-r from-[#f8f8f8] via-[#f4f4f4] to-[#f0f0f0] dark:from-[#212123] dark:via-[#18181a] dark:to-[#141416] z-10 flex flex-col"
                >
                  {renderFlyoutContent(flyout, data)}
                </motion.div>
              )}
            </AnimatePresence>
          </>
        )}
      </div>
    );
  }

  // Regular sidebar for non-icon modes
  return (
    <Sidebar
      side="left"
      variant={variant}
      collapsible={collapsible}
      className={cn("border-r", className)}
      {...props}
    >
      <SidebarHeader className="border-b">
        <TeamSwitcher teams={data.teams} />
      </SidebarHeader>
      
      <SidebarContent>
        <NavMain items={data.navMain} />
        <NavProjects projects={data.projects} />
      </SidebarContent>
      
      <SidebarFooter>
        <NavUser user={data.user} />
      </SidebarFooter>
    </Sidebar>
  );
}

// Function to calculate student count percentage (placeholder for now)
const getStudentCountPercentage = () => {
  // TODO: Replace with actual student count logic
  // For now, return a mock percentage based on current students vs max capacity
  const currentStudents = 24; // Mock current student count
  const maxCapacity = 30; // Mock max capacity for teacher
  return (currentStudents / maxCapacity) * 100;
};

function renderFlyoutContent(flyout: string | null, data: any) {
  if (!flyout) return null;
  
  switch (flyout) {
    case "notifications":
      return (
        <div className="flex py-2 flex-col h-full">
          <div className="p-2 px-4 border-b border-black/20 dark:border-white/20">
            <h2 className="text-xl text-left font-manrope_1 font-semibold text-black/80 dark:text-white/80">
              Calendar layout coming
            </h2>
          </div>
        
        </div>
      );
    


    case "discover":
      return (
        <div className="flex py-2 flex-col h-full">
         <div className="p-2 px-4 border-b border-black/20 dark:border-white/20">
            <h2 className="text-xl text-left font-manrope_1 font-semibold text-black/80 dark:text-white/80">
              Discover
            </h2>          
          </div>
          <div className="flex-1 p-4 overflow-auto">
            <div className="space-y-2">
              <div className="p-3 rounded-md bg-sidebar-accent/50 hover:bg-sidebar-accent cursor-pointer">
                <p className="text-sm font-medium">New Features</p>
              </div>
              <div className="p-3 rounded-md bg-sidebar-accent/50 hover:bg-sidebar-accent cursor-pointer">
                <p className="text-sm font-medium">Training Resources</p>
              </div>
            </div>
          </div>
        </div>
      );

    case "new":
      return (
        <div className="flex py-2 flex-col h-full">
          <div className="p-2 px-4 border-b border-black/20 dark:border-white/20">
            <button className="text-sm w-full text-center rounded-lg py-2 font-manrope_1 font-semibold text-white/80 dark:text-black/80 bg-gradient-to-b from-[#c7c7c7] via-[#d9d9d9] to-[#ececec]">
              New Assignment
            </button>
          </div>
          <div className="flex-1 p-4 overflow-auto">
            <div className="space-y-2">
              <button className="w-full text-left p-2 rounded-md hover:bg-sidebar-accent">
                New Student
              </button>
              <button className="w-full text-left p-2 rounded-md hover:bg-sidebar-accent">
                New Teacher
              </button>
              <button className="w-full text-left p-2 rounded-md hover:bg-sidebar-accent">
                New Class
              </button>
            </div>
          </div>
        </div>
      );

    case "status":
      return (
        <div className="flex py-2 flex-col h-full">
          <div className="p-2 px-4 border-b border-black/20 dark:border-white/20">
            <h2 className="text-xl text-left font-manrope_1 font-semibold text-black/80 dark:text-white/80">
              System Status
            </h2>
          </div>
          <div className="flex-1 p-4 overflow-auto">
            <div className="space-y-3">
              <div className="p-3 rounded-md bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800">
                <div className="flex items-center gap-2">
                  <Circle className="w-3 h-3 fill-green-500 text-green-500" />
                  <span className="text-sm font-medium text-green-800 dark:text-green-200">All Systems Operational</span>
                </div>
              </div>
              <div className="p-3 rounded-md bg-sidebar-accent/50">
                <p className="text-sm font-medium mb-1">Student Management</p>
                <p className="text-xs text-gray-600 dark:text-gray-400">24/30 students enrolled</p>
              </div>
              <div className="p-3 rounded-md bg-sidebar-accent/50">
                <p className="text-sm font-medium mb-1">Class Sessions</p>
                <p className="text-xs text-gray-600 dark:text-gray-400">5 active classes</p>
              </div>
              <div className="p-3 rounded-md bg-sidebar-accent/50">
                <p className="text-sm font-medium mb-1">Assignments</p>
                <p className="text-xs text-gray-600 dark:text-gray-400">12 pending reviews</p>
              </div>
            </div>
          </div>
        </div>
      );

    case "classes":
      return (
        <div className="flex py-2 flex-col h-full">
          <div className="p-2 px-4 border-b border-black/20 dark:border-white/20">
            <h2 className="text-xl text-left font-manrope_1 font-semibold text-black/80 dark:text-white/80">
              classes
            </h2>
          </div>
          <div className="flex-1 overflow-auto">
            <p>
               - a place for the teacher to add his/hers classes
            </p>
            <NavProjects
              projects={data.projects}
            />
          </div>
        </div>
      );

    case "students-gauge":
      return (
        <div className="flex py-2 flex-col h-full">
          <div className="p-2 px-4 border-b border-black/20 dark:border-white/20">
            <h2 className="text-xl text-left font-manrope_1 font-semibold text-black/80 dark:text-white/80">
              Student Overview
            </h2>
          </div>
          <div className="flex-1 p-4 overflow-auto">
            <div className="space-y-4">
              <div className="p-3 rounded-md bg-sidebar-accent/50">
                <div className="flex justify-between items-center mb-2">
                  <span className="text-sm font-medium">Current Students</span>
                  <span className="text-lg font-bold">24</span>
                </div>
                <div className="flex justify-between items-center mb-2">
                  <span className="text-sm font-medium">Max Capacity</span>
                  <span className="text-lg font-bold">30</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div
                    className="bg-blue-600 h-2 rounded-full"
                    style={{ width: `${getStudentCountPercentage()}%` } as React.CSSProperties}
                  ></div>
                </div>
                <p className="text-xs text-gray-600 mt-2">
                  {Math.round(getStudentCountPercentage())}% capacity used
                </p>
              </div>
              <div className="p-3 rounded-md bg-sidebar-accent/50 hover:bg-sidebar-accent cursor-pointer">
                <p className="text-sm font-medium">Manage Students</p>
                <p className="text-xs text-gray-600">Add, remove, or edit student information</p>
              </div>
              <div className="p-3 rounded-md bg-sidebar-accent/50 hover:bg-sidebar-accent cursor-pointer">
                <p className="text-sm font-medium">Student Analytics</p>
                <p className="text-xs text-gray-600">View performance and attendance data</p>
              </div>
            </div>
          </div>
        </div>
      );

    default:
      return (
        <div className="flex items-center justify-center h-full">
          <p className="text-muted-foreground">Select a panel</p>
        </div>
      );
  }
}