//components/ui/canvas-reveal-effect.tsx
// DISABLED: React Three Fiber compatibility issues with experimental React version
// This component has been replaced with CSS-based animations in the sidebar

import { cn } from "../../lib/utils";

export const CanvasRevealEffect = ({
  animationSpeed = 0.4,
  opacities = [0.3, 0.3, 0.3, 0.5, 0.5, 0.5, 0.8, 0.8, 0.8, 1],
  colors = [[0, 255, 255]],
  containerClassName,
  dotSize,
  showGradient = true,
}: {
  /**
   * 0.1 - slower
   * 1.0 - faster
   */
  animationSpeed?: number;
  opacities?: number[];
  colors?: number[][];
  containerClassName?: string;
  dotSize?: number;
  showGradient?: boolean;
}) => {
  // Fallback CSS-based implementation
  return (
    <div className={cn("h-full relative bg-black w-full", containerClassName)}>
      <div className="absolute inset-0 overflow-hidden">
        <div
          className="absolute inset-0 animate-pulse opacity-30"
          style={{
            backgroundImage: `radial-gradient(circle at 1px 1px, rgba(244, 244, 245, 0.8) 1px, transparent 0)`,
            backgroundSize: '12px 12px'
          } as React.CSSProperties}
        />
        <div
          className="absolute inset-0 animate-pulse opacity-20"
          style={{
            backgroundImage: `radial-gradient(circle at 1px 1px, rgba(250, 250, 250, 0.6) 1px, transparent 0)`,
            backgroundSize: '16px 16px',
            animationDelay: '1s'
          } as React.CSSProperties}
        />
        <div
          className="absolute inset-0 animate-pulse opacity-10"
          style={{
            backgroundImage: `radial-gradient(circle at 1px 1px, rgba(228, 228, 231, 0.4) 1px, transparent 0)`,
            backgroundSize: '20px 20px',
            animationDelay: '2s'
          } as React.CSSProperties}
        />
      </div>
      {showGradient && (
        <div className="absolute inset-0 bg-gradient-to-t from-gray-950 to-[84%]" />
      )}
    </div>
  );
};

// All Three.js related code removed due to React compatibility issues
// The component now uses CSS-based animations only
